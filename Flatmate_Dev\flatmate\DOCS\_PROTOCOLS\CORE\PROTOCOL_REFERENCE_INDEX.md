# Protocol Reference Index

**Purpose**: Central index of all development protocols, workflows, and templates  
**Maintained**: Updated after each protocol creation or modification  
**Version**: 1.0  
**Last Updated**: 2025-01-20

---

## Core Development Protocols

### **Work Session Management**
| Protocol | Location | Purpose | Status |
|----------|----------|---------|--------|
| **Unified Work Session Protocol** | `.augment/rules/unified-work-session-protocol.md` | Master protocol for all work sessions | ✅ Active |
| **Session Documentation Guide** | `flatmate/DOCS/_PROTOCOLS/GUIDES/session_documentation_guide.md` | Real-time session documentation | ✅ Active |
| **Workflow Quick Start** | `flatmate/DOCS/_ARCHITECTURE/WORKFLOW_QUICK_START.md` | Get started immediately | ✅ Active |

### **Feature Development**
| Protocol | Location | Purpose | Status |
|----------|----------|---------|--------|
| **Feature Protocol v1.1** | `.augment/rules/feature-protocol_v1.1.md` | Complete feature development process | ✅ Active |
| **Documentation Update Protocol** | `.augment/rules/update-docs.md` | Post-session documentation workflow | ✅ Active |

### **Troubleshooting & Debugging**
| Protocol | Location | Purpose | Status |
|----------|----------|---------|--------|
| **Troubleshooting Workflow** | `.windsurf/workflows/trouble-shoot.md` | Systematic problem-solving | ✅ Active |

### **GUI Component Development**
| Protocol | Location | Purpose | Status |
|----------|----------|---------|--------|
| **GUI Component Creation Protocol** | `flatmate/DOCS/_ARCHITECTURE/GUI_COMPONENT_CREATION_PROTOCOL.md` | Industry-standard component patterns | ✅ Active |
| **GUI Component Quick Reference** | `flatmate/DOCS/_ARCHITECTURE/GUI_COMPONENT_QUICK_REFERENCE.md` | Fast component creation guide | ✅ Active |

### **Self-Improvement System**
| Protocol | Location | Purpose | Status |
|----------|----------|---------|--------|
| **Self-Improvement Protocol** | `.augment/rules/self-improvement-protocol.md` | Systematic lesson capture and application | ✅ Active |
| **Self-Improvement Quick Reference** | `flatmate/DOCS/_ARCHITECTURE/SELF_IMPROVEMENT_QUICK_REFERENCE.md` | Daily/weekly improvement practices | ✅ Active |
| **Weekly Lesson Review Script** | `scripts/weekly_lesson_review.py` | Automated lesson analysis | ✅ Active |

---

## Templates & Quick References

### **Documentation Guides**
| Guide | Location | Purpose | Usage |
|-------|----------|---------|-------|
| **Project Documentation Guide** | `flatmate/DOCS/_PROTOCOLS/GUIDES/project_documentation_guide.md` | Standard templates for all docs | Copy templates for new docs |
| **Session Documentation Guide** | `flatmate/DOCS/_PROTOCOLS/GUIDES/session_documentation_guide.md` | Real-time session documentation | Copy template for each session |
| **Report Writing Guide** | `flatmate/DOCS/_PROTOCOLS/GUIDES/report_writing_guide.md` | Analysis and assessment reports | Use templates for reports |

### **Quick Reference Guides**
| Guide | Location | Purpose | When to Use |
|-------|----------|---------|-------------|
| **Workflow Quick Start** | `flatmate/DOCS/_ARCHITECTURE/WORKFLOW_QUICK_START.md` | Immediate implementation guide | Starting new workflow |
| **GUI Component Quick Reference** | `flatmate/DOCS/_ARCHITECTURE/GUI_COMPONENT_QUICK_REFERENCE.md` | Fast component creation | Creating GUI components |
| **Self-Improvement Quick Reference** | `flatmate/DOCS/_ARCHITECTURE/SELF_IMPROVEMENT_QUICK_REFERENCE.md` | Daily improvement practices | Every work session |

---

## Protocol Integration Map

### **Session Workflow Integration**
```
Start Session → Unified Work Session Protocol
    ├─ Feature Work → Feature Protocol v1.1
    ├─ Refactoring → GUI Component Protocol (if applicable)
    ├─ Troubleshooting → Troubleshooting Workflow
    └─ End Session → Documentation Update Protocol + Self-Improvement
```

### **Documentation Workflow Integration**
```
Work Session → Session Log (real-time)
    └─ Session End → Changelog + Lessons Learned
        └─ Weekly → Lesson Review + Protocol Updates
            └─ Monthly → Protocol Evolution
```

### **Component Development Integration**
```
New Component → GUI Component Creation Protocol
    ├─ Classification → Base Class vs Reusable Component
    ├─ Structure → Folder organization and naming
    ├─ Implementation → Configuration, styling, testing
    └─ Integration → Update architecture docs
```

---

## File Organization Strategy

### **Current Structure** (Needs Consolidation)
```
.augment/rules/                    # AI assistant rules and core protocols
├── unified-work-session-protocol.md
├── feature-protocol_v1.1.md
├── update-docs.md
└── self-improvement-protocol.md

.windsurf/workflows/               # IDE-specific workflows
└── trouble-shoot.md

flatmate/DOCS/_ARCHITECTURE/       # Reference documentation
├── GUI_COMPONENT_CREATION_PROTOCOL.md
├── GUI_COMPONENT_QUICK_REFERENCE.md
├── SELF_IMPROVEMENT_QUICK_REFERENCE.md
├── WORKFLOW_QUICK_START.md
└── PROTOCOL_REFERENCE_INDEX.md (this file)

scripts/                          # Automation scripts
└── weekly_lesson_review.py
```

### **Recommended Consolidation** (Future)
```
flatmate/DOCS/_PROTOCOLS/         # All protocols in one place
├── CORE/                         # Core development protocols
│   ├── unified-work-session-protocol.md
│   ├── feature-protocol.md
│   ├── troubleshooting-protocol.md
│   └── documentation-protocol.md
├── SPECIALIZED/                  # Domain-specific protocols
│   ├── gui-component-protocol.md
│   └── self-improvement-protocol.md
├── GUIDES/                       # All documentation guides
│   ├── session-documentation-guide.md
│   ├── project-documentation-guide.md
│   ├── report-writing-guide.md
│   └── component-creation-guide.md
├── QUICK_REFERENCE/             # Quick reference guides
│   ├── workflow-quick-start.md
│   ├── component-quick-reference.md
│   └── improvement-quick-reference.md
└── INDEX.md                     # This reference index
```

---

## Usage Guidelines

### **For New Developers**
1. **Start Here**: Read `WORKFLOW_QUICK_START.md`
2. **Understand Protocols**: Review `unified-work-session-protocol.md`
3. **Use Guides**: Copy templates from `session_documentation_guide.md`
4. **Reference Index**: Use this file to find specific protocols

### **For AI Assistants**
1. **Session Start**: Reference `unified-work-session-protocol.md`
2. **Specific Work**: Use appropriate specialized protocol
3. **Session End**: Follow `update-docs.md` workflow
4. **Find Protocols**: Use this index to locate specific guidance

### **For Protocol Updates**
1. **Update Protocol**: Modify the specific protocol file
2. **Update Index**: Update this reference index
3. **Version Control**: Document changes in protocol evolution log
4. **Communicate**: Update team on significant changes

---

## Protocol Maintenance

### **Regular Reviews**
- **Weekly**: Check if protocols are being followed
- **Monthly**: Review protocol effectiveness and update as needed
- **Quarterly**: Major protocol evolution based on accumulated lessons

### **Version Control**
- **All protocols are version controlled** in git
- **Changes are documented** in commit messages
- **Major changes trigger** protocol evolution log updates

### **Quality Assurance**
- **Protocols are tested** in real development sessions
- **Feedback is collected** through lesson learned system
- **Improvements are systematic** based on actual usage data

---

## Quick Access Commands

### **Find a Protocol**
```bash
# Search for specific protocol
grep -r "protocol_name" flatmate/DOCS/_ARCHITECTURE/
grep -r "protocol_name" .augment/rules/
```

### **Start New Session**
```bash
# Copy session documentation guide
cp "flatmate/DOCS/_PROTOCOLS/GUIDES/session_documentation_guide.md" "flatmate/DOCS/_FEATURES/SESSION_NAME/SESSION_LOG.md"
```

### **Run Weekly Review**
```bash
# Automated lesson review
python scripts/weekly_lesson_review.py
```

---

## Integration with Development Tools

### **VS Code Integration**
- **Snippets**: Create snippets for common protocol patterns
- **Tasks**: Set up tasks for protocol execution
- **Extensions**: Use markdown extensions for better protocol editing

### **Git Integration**
- **Commit Templates**: Include protocol compliance in commit messages
- **Hooks**: Validate protocol compliance before commits
- **Branches**: Use protocol-based branch naming

### **AI Assistant Integration**
- **Context**: Provide protocol context at session start
- **Validation**: Check protocol compliance during work
- **Updates**: Apply protocol updates systematically

---

## Success Metrics

### **Protocol Adoption**
- **Usage Rate**: % of sessions following protocols
- **Compliance**: % of protocol steps completed
- **Effectiveness**: Time saved through protocol use

### **Quality Improvements**
- **Documentation Quality**: Completeness and clarity
- **Code Quality**: Architecture and maintainability
- **Process Efficiency**: Time to complete common tasks

### **Developer Experience**
- **Satisfaction**: How enjoyable the development process is
- **Confidence**: How confident developers are in their decisions
- **Learning**: How quickly new patterns and practices are adopted

---

**This index is the single source of truth for all development protocols. Keep it updated as protocols evolve.**
