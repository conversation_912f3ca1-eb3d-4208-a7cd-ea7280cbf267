# Update Data Module – Left Panel Checkbox Definition

## Location
- **File:** `src/fm/modules/update_data/_view/left_panel/widgets/widgets.py`
- **Class:** `LeftPanelButtonsWidget`

## Checkbox Details
- The left panel includes a checkbox labelled **"Update Database"**.
- This is implemented using the `LabeledCheckBox` widget, imported from `fm.gui._shared_components.widgets.checkboxes`.
- Instantiation (in `_init_ui`):
  ```python
  self.db_update_checkbox = LabeledCheckBox(
      label_text="Update Database",
      checked=True,
      tooltip="Store processed transactions in the central database"
  )
  ```
- The checkbox is added to the main vertical layout of the panel.
- Its state change emits the `update_database_changed` signal.

## Other Labels
- All other labels in the left panel are created with standard `QLabel` widgets from PySide6, e.g.:
  ```python
  self.title = QLabel("Update Data")
  self.source_label = QLabel("1. Source Files")
  self.save_label = QLabel("2. Save Location")
  self.process_label = QLabel("3. Process")
  ```
- No custom label base primitive is used for these; they are direct `QLabel` instances, sometimes with an `objectName` set for styling.

## Purpose
- Allows the user to control whether processed transactions are stored in the central database during the update data workflow.

## UI Context
- Appears below the source and save location controls, above the process button.

---

## Proposed Shared Widgets Folder Structure

### QSS File Placement

QSS files (Qt style sheets) should be stored in a dedicated `styles/` or `resources/` directory at the top level of the project, not mixed with Python modules. For example:

```
flatmate/
├── src/
│   └── fm/
│       └── gui/
│           └── _shared_components/
│               └── widgets/
│                   └── ... (Python files only)
├── styles/
│   ├── base.qss
│   ├── buttons.qss
│   └── ...
```

**Rationale:**
- Keeps code and assets clearly separated.
- Makes it easier to manage, update, and load QSS at runtime.
- Follows the convention of treating QSS like static assets (similar to CSS in web projects).

*QSS files should live in a dedicated styles/ or resources/ folder, not mixed with Python modules. This is standard in larger Qt/PySide projects.*



To improve consistency and follow Qt/QSS best practice, refactor the shared widgets folder as follows:

```
widgets/
│
├── __init__.py
│
├── buttons/
│   ├── __init__.py
│   └── action_button.py
│   └── secondary_button.py
│   └── exit_button.py
│
├── checkboxes/
│   ├── __init__.py
│   └── labeled_checkbox.py
│
├── labels/
│   ├── __init__.py
│   └── heading_label.py
│
├── inputs/
│   ├── __init__.py
│   ├── text_input.py
│   └── combo_box.py
│
├── option_menus/
│   ├── __init__.py
│   └── option_menu_with_label.py
│
├── selectors/
│   ├── __init__.py
│   └── account_selector.py
│
├── filters/
│   ├── __init__.py
│   └── date_filter_pane.py
│
├── base/
│   ├── __init__.py
│   └── base_widget.py
│
├── styles/
│   ├── base.qss
│   ├── buttons.qss
│   ├── checkboxes.qss
│   ├── labels.qss
│   └── option_menus.qss
│
└── README.md
```

- Each widget type gets its own subfolder.
- QSS styles are modular and separated by widget type.
- Each widget class is in its own file.
- Add a `labels/` folder for consistent heading/subheading widgets.
- Keep a clear `README.md` with conventions.

*This structure supports maintainability, QSS styling consistency, and clear separation of concerns.*

*Last reviewed: 2025-07-22*
